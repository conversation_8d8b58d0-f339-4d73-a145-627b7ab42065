'use client';

import { useState, useCallback, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import {
  Upload,
  CheckCircle,
  Settings,
  <PERSON><PERSON><PERSON>,
  Download,
  ChevronRight,
  Loader2
} from 'lucide-react';

import { setToastAlert } from '@/slices/metaDataSlice';

import Ingestion from './ingestion';
import Validation from './validation';
import Transformation from './transformation';
import Distribution from './distribution';
import Export from './export';

const WorkflowTabs = () => {
  const dispatch = useDispatch();
  
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [workflowData, setWorkflowData] = useState<any>({});
  // const [loading, setLoading] = useState(false);

  // Utility functions
  const showSuccess = (message: string) => {
    dispatch(setToastAlert({ isToastOpen: true, intent: 'success', title: 'Success', content: message }));
  };

  // const showError = (message: string, error?: any) => {
  //   const errorMessage = error?.response?.data?.message || error?.message || message;
  //   dispatch(setToastAlert({ isToastOpen: true, intent: 'error', title: 'Error', content: errorMessage }));
  // };

  const goToNextStep = useCallback(() => {
    setSelectedIndex(prev => prev < 4 ? prev + 1 : prev);
  }, []);

  const handleFileUpload = useCallback((files: any) => {
    setWorkflowData(prev => ({ ...prev, uploadedFiles: files }));
  }, []);

  const handleValidationComplete = useCallback((data: any) => {
    setWorkflowData(prev => ({ ...prev, validationData: data }));
  }, []);

  const handleTransformationComplete = useCallback((data: any) => {
    setWorkflowData(prev => ({ ...prev, transformationData: data }));
  }, []);

  const handleDistributionComplete = useCallback((data: any) => {
    setWorkflowData(prev => ({ ...prev, distributionData: data }));
  }, []);

  const handleExportComplete = useCallback((data: any) => {
    setWorkflowData(prev => ({ ...prev, exportData: data }));
    showSuccess('Workflow completed successfully!');
  }, [showSuccess]);


  const getStepStatus = useCallback((stepIndex: number) => {
    if (selectedIndex > stepIndex) {
      return 'completed';
    } else if (selectedIndex === stepIndex) {
      return 'active';
    } else {
      return 'pending';
    }
  }, [selectedIndex]);

  const getStepDescription = useCallback((stepIndex: number, originalDescription: string) => {
    if (selectedIndex > stepIndex) {
      return 'Complete';
    }
    return originalDescription;
  }, [selectedIndex]);

  const workflowSteps = useMemo(() => [
    {
      id: 0,
      title: 'Ingestion',
      description: getStepDescription(0, 'Upload files'),
      component: <Ingestion onNext={goToNextStep} onFileUpload={handleFileUpload} />,
      icon: Upload,
      status: getStepStatus(0)
    },
    {
      id: 1,
      title: 'Validation',
      description: getStepDescription(1, 'Validate data'),
      component: <Validation onNext={goToNextStep} uploadedFiles={workflowData?.uploadedFiles || null} onValidationComplete={handleValidationComplete} />,
      icon: CheckCircle,
      status: getStepStatus(1)
    },
    {
      id: 2,
      title: 'Transformation',
      description: getStepDescription(2, 'Apply rules'),
      component: <Transformation onNext={goToNextStep} uploadedFiles={workflowData?.validationData || workflowData?.uploadedFiles || null} onTransformationComplete={handleTransformationComplete} />,
      icon: Settings,
      status: getStepStatus(2)
    },
    {
      id: 3,
      title: 'Distribution',
      description: getStepDescription(3, 'Calculate allocation'),
      component: <Distribution onNext={goToNextStep} uploadedFiles={workflowData?.transformationData || workflowData?.validationData || workflowData?.uploadedFiles || null} onDistributionComplete={handleDistributionComplete} />,
      icon: PieChart,
      status: getStepStatus(3)
    },
    {
      id: 4,
      title: 'Export',
      description: getStepDescription(4, 'Generate reports'),
      component: <Export onNext={goToNextStep} uploadedFiles={workflowData?.distributionData || workflowData?.transformationData || workflowData?.validationData || workflowData?.uploadedFiles || null} onExportComplete={handleExportComplete} />,
      icon: Download,
      status: getStepStatus(4)
    }
  ], [selectedIndex, workflowData, goToNextStep, handleFileUpload, handleValidationComplete, handleTransformationComplete, handleDistributionComplete, handleExportComplete, getStepStatus, getStepDescription]);

  const getStepColor = useCallback((status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-white text-green-600 border-2 border-green-500';
      case 'processing':
        return 'bg-white text-blue-600 border-2 border-blue-500 animate-pulse';
      case 'active':
        return 'bg-white text-blue-600 border-2 border-blue-500';
      default:
        return 'bg-white text-gray-400 border-2 border-gray-300';
    }
  }, []);

  const getConnectorColor = useCallback((currentIndex: number) => {
    if (selectedIndex > currentIndex) {
      return 'bg-green-500';
    } else if (selectedIndex === currentIndex) {
      return 'bg-blue-500';
    } else {
      return 'bg-gray-300';
    }
  }, [selectedIndex]);

  return (
    <div className="h-full flex flex-col overflow-hidden">
      {/* Step Progress Bar */}
      <div className="px-6 py-2 flex justify-center flex-shrink-0">
        <div className="flex items-center justify-center">
          {workflowSteps.map((step, index) => {
            const IconComponent = step.icon;
            return (
              <div key={step.id} className="flex items-center">
                <div
                  className={`flex items-center space-x-3 transition-all duration-500 ease-in-out cursor-default ${
                    index <= selectedIndex ? 'opacity-100' : 'opacity-50'
                  } ${index === selectedIndex ? 'ring-2 ring-blue-300 rounded-lg p-1' : ''}`}
                >
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 ${getStepColor(step.status)}`}>
                    {step.status === 'processing' ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <IconComponent className="w-4 h-4" />
                    )}
                  </div>
                  <div className="hidden md:block">
                    <p className="text-xs font-medium text-gray-800">{step.title}</p>
                    <p className="text-xs text-gray-600">{step.description}</p>
                  </div>
                </div>
                {index < workflowSteps.length - 1 && (
                  <div className="flex items-center mx-2">
                    <div className={`h-0.5 w-8 transition-all duration-500 ${getConnectorColor(index)}`}></div>
                    <ChevronRight className="w-3 h-3 text-gray-400 mx-1" />
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Step Content */}
      <div className="flex-1 overflow-hidden">
        {workflowSteps[selectedIndex]?.component}
      </div>
    </div>
  );
};

export default WorkflowTabs;
